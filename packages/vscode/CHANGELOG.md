# Change Log

## 0.6.0

-   add file resolver language server
    -   add xml file attributes("file", "file_name", "filename", "fileref") jump

## 0.5.0

-   add angelscript language server
    -   add `#include ` definition jump

## 0.4.0

-   optimize format error output
-   add command for "Show File Format output"

## 0.3.0

-   optimize format performance
-   add format error output

## 0.2.0

-   add file scan loading percent tip
-   add format command

## 0.1.0

-   add file scan loading
-   fixed file scan check cause vscode lag
-   add tempalte code snippets(prefix: `rwr-`)

## 0.0.2

-   add more file ref scan property("@\_file", "@\_file_name", "@\_filename", "@\_fileref")
