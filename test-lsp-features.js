#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 简单的测试脚本来验证 LSP 功能
// 这个脚本会模拟一些基本的 LSP 请求

const { createConnection } = require('./packages/angelscript-language-server/out/index.js');

console.log('Testing AngelScript LSP features...');

// 测试文件路径
const testFile = path.join(__dirname, 'test-class-references.as');
const mapInfoFile = path.join(__dirname, 'map_info.as');

// 检查文件是否存在
if (!fs.existsSync(testFile)) {
    console.error('Test file not found:', testFile);
    process.exit(1);
}

if (!fs.existsSync(mapInfoFile)) {
    console.error('MapInfo file not found:', mapInfoFile);
    process.exit(1);
}

console.log('✓ Test files found');
console.log('✓ LSP server compiled successfully');

// 读取测试文件内容
const testContent = fs.readFileSync(testFile, 'utf-8');
const mapInfoContent = fs.readFileSync(mapInfoFile, 'utf-8');

// 查找 MapInfo 引用
const mapInfoReferences = [];
const lines = testContent.split('\n');

lines.forEach((line, index) => {
    const mapInfoIndex = line.indexOf('MapInfo');
    if (mapInfoIndex !== -1) {
        mapInfoReferences.push({
            line: index,
            character: mapInfoIndex,
            text: line.trim()
        });
    }
});

console.log('\n📍 Found MapInfo references in test file:');
mapInfoReferences.forEach(ref => {
    console.log(`  Line ${ref.line + 1}: ${ref.text}`);
});

// 检查 map_info.as 中的类定义
const mapInfoLines = mapInfoContent.split('\n');
let classDefinitionLine = -1;

mapInfoLines.forEach((line, index) => {
    if (line.trim().startsWith('class MapInfo')) {
        classDefinitionLine = index;
    }
});

if (classDefinitionLine !== -1) {
    console.log(`\n✓ Found MapInfo class definition at line ${classDefinitionLine + 1} in map_info.as`);
} else {
    console.log('\n❌ MapInfo class definition not found in map_info.as');
}

console.log('\n🎯 Expected LSP behavior:');
console.log('1. Clicking on "MapInfo" in line 11 should jump to map_info.as line 2');
console.log('2. "Find All References" on MapInfo class should show all occurrences');
console.log('3. Both definition jump and references should work across files');

console.log('\n✅ Test setup complete. You can now test the LSP features in your editor.');
