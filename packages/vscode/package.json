{"name": "vscode-rwr-mod-tool", "displayName": "RWR Mod Tool", "description": "Running with Rifles game mod tool", "version": "0.6.0", "publisher": "Kreedzt", "pricing": "Free", "license": "MIT", "engines": {"vscode": "^1.80.0"}, "categories": ["Snippets", "Linters", "Other"], "keywords": ["Running with Rifles", "rwr", "mod", "AngelScript"], "activationEvents": ["workspaceContains:**/calls/all_calls.xml", "workspaceContains:**/factions/all_factions.xml", "workspaceContains:**/items/all_carry_items.xml", "workspaceContains:**/weapons/all_weapons.xml", "onLanguage:angelscript"], "main": "./dist/client.js", "repository": {"type": "git", "url": "https://github.com/Kreedzt/vscode-rwr-mod-tool", "directory": "packages/vscode"}, "contributes": {"languages": [{"id": "angelscript", "extensions": [".as"], "aliases": ["AngelScript", "angelscript"], "configuration": "./language-configuration.json"}, {"id": "xml", "extensions": [".xml", ".base", ".weapon", ".carry_item", ".models", ".call"]}], "grammars": [{"language": "angelscript", "scopeName": "source.angelscript", "path": "./syntaxes/angelscript.tmLanguage.json"}], "commands": [{"command": "vscode-rwr-mod-tool.createArmor", "title": "RWR Mod Tool: Create Armor"}, {"command": "vscode-rwr-mod-tool.createWeapon", "title": "RWR Mod Tool: Create Weapon"}, {"command": "vscode-rwr-mod-tool.formatActiveFile", "title": "RWR Mod Tool: Format Active File"}, {"command": "vscode-rwr-mod-tool.formatWorkspace", "title": "RWR Mod Tool: Format Workspace"}, {"command": "vscode-rwr-mod-tool.showFileFormatOutput", "title": "RWR Mod Tool: Show File Format Output"}], "snippets": [{"language": "xml", "path": "./snippets/call.json"}, {"language": "xml", "path": "./snippets/faction.json"}, {"language": "xml", "path": "./snippets/item.json"}, {"language": "xml", "path": "./snippets/language.json"}, {"language": "xml", "path": "./snippets/map.json"}, {"language": "xml", "path": "./snippets/model.json"}, {"language": "xml", "path": "./snippets/vehicle.json"}, {"language": "xml", "path": "./snippets/weapon.json"}, {"language": "xml", "path": "./snippets/misc.json"}], "walkthroughs": [{"id": "tutorial", "title": "Get started with RWR Mod Tool", "description": "Learn how to use RWR Mod Tool", "steps": [{"id": "run-command", "title": "Run a command", "description": "Run a command from the command palette", "media": {"image": "./media/commands.png", "altText": "Command palette"}}, {"id": "file-ref-check", "title": "Check file references", "description": "Auto check file references", "media": {"image": "./media/file-ref-check.png", "altText": "File reference check"}}, {"id": "snippets", "title": "All snippets are prefixed with rwr-", "media": {"image": "./media/snippets.png", "altText": "Snippets"}}]}]}, "scripts": {"build": "node scripts/build -- --minify", "watch": "npm run build -- --watch", "pack": "npm run build && vsce package --no-dependencies", "release": "npm run build && vsce publish --no-dependencies", "pretest": "pnpm run compile && pnpm run lint", "format": "prettier --write \"src/**/*.ts\"", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js"}, "devDependencies": {"@angelscript/language-server": "workspace:*", "@file-resolver/language-server": "workspace:*", "@types/glob": "^8.1.0", "@types/lodash": "^4.14.202", "@types/mocha": "^10.0.1", "@types/node": "20.2.5", "@types/vscode": "^1.80.0", "@typescript-eslint/eslint-plugin": "^5.59.8", "@typescript-eslint/parser": "^5.59.8", "@volar/language-server": "~2.4.0", "@volar/vscode": "~2.4.0", "@vscode/test-electron": "^2.3.2", "esbuild": "^0.25.0", "eslint": "^8.41.0", "glob": "^8.1.0", "mocha": "^10.2.0", "typescript": "^5.1.3"}, "dependencies": {"@prettier/plugin-xml": "^3.2.2", "fast-xml-parser": "^4.2.7", "lodash": "^4.17.21", "prettier": "^3.1.1", "rxjs": "^7.8.1", "vscode-languageclient": "^9.0.1"}}