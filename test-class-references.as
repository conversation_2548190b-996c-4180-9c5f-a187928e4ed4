// internal
#include "log.as"
#include "helpers.as"
#include "map_info.as"

// --------------------------------------------
class Metagame {
    protected Comms@ m_comms;
    protected TaskSequencer@ m_taskSequencer;
    protected array<TaskSequencer@> m_hd_taskSequencerArray;
    protected TaskManager@ m_taskManager;
    protected MapInfo m_mapInfo;

    protected array<Tracker@> m_trackers;
    protected bool m_restartRequested;

    protected ModeratorManager@ m_moderatorManager;
    protected AdminManager@ m_adminManager;
    protected BanManager@ m_banManager;

    protected string m_startServerCommand;
    protected bool m_gamePaused;

    // --------------------------------------------
    Metagame(string startServerCommand = "") {
        m_gamePaused = false;

        @m_moderatorManager = ModeratorManager(this);
        @m_adminManager = AdminManager(this);

        m_startServerCommand = startServerCommand;
        @m_comms = Comms();

        if (isInServerMode()) {
            startServer();
            getAdminManager().loadFromFile();
            getModeratorManager().loadFromFile();
        }

        m_restartRequested = false;
    }

    // --------------------------------------------
    void init() {
        _log("metagame init");
        clearTrackers();

        @m_taskSequencer = TaskSequencer();
        @m_taskManager = TaskManager();
        for(uint i=20;i>0;--i){
            m_hd_taskSequencerArray.insertLast(TaskSequencer());
        }

        resetTimer();
    }

    // --------------------------------------------
    Comms@ getComms() const {
        return m_comms;
    }

    // --------------------------------------------
    TaskSequencer@ getTaskSequencer() const {
        return m_taskSequencer;
    }

    // --------------------------------------------
    TaskManager@ getTaskManager() const {
        return m_taskManager;
    }

    // --------------------------------------------
    TaskSequencer@ getHdTaskSequncerIndex(uint i) const {
        if(i >= m_hd_taskSequencerArray.size()){
            return null;
        }
        return m_hd_taskSequencerArray[i];
    }

    // --------------------------------------------
    void resetTimer() {
        float dummy = now();
    }

    // --------------------------------------------
    bool run() {
        const float TARGET_CYCLE_TIME = 0.001f;
        const float MINIMUM_SLEEP_TIME = 0.001f;
        float dummy = now();
        bool processed = false;
        m_gamePaused = false;
        _log("start run()");

        while (!g_restartMetagame) {
            float elapsedTime = !m_gamePaused ? now() : 0.0f;
            float sleepTime = MINIMUM_SLEEP_TIME;

            if (elapsedTime > 0.0f) {
                update(elapsedTime);

                if (!processed) {
                    sleepTime = TARGET_CYCLE_TIME - elapsedTime;
                }
            }

            if (sleepTime > 0.0f) {
                sleep(sleepTime);
            }

            processed = false;
            const XmlElement@ event = m_comms.receive();

            if (!event.empty() && event.getName() != "dummy_event") {
                if (m_trackers.size() > 0) {
                    processed = true;

                    if (event.getName() == "user_quit_event") {
                        _log("user quit signal detected, stopping script", -1);
                        _quitSignalReceived = true;
                        break;
                    } else if (event.getName() == "pause_event") {
                        m_gamePaused = event.getBoolAttribute("state");
                        now();
                    }

                    // inform all trackers about the event
                    for (uint i = 0; i < m_trackers.size(); ++i) {
                        Tracker@ tracker = m_trackers[i];
                        tracker.handleEvent(event);
                    }
                } else {
                    _log("no event tracker registered, skipping message", 1);
                }
            }

            if (m_restartRequested) {
                m_restartRequested = false;
                _log("restart had been requested, calling metagame init now", -1);
                init();
            }

            if(g_restartMetagame){
                g_restartMetagame = !g_restartMetagame;
                break;
            }
        }

        _log("break run()");

        if(isInServerMode() && !g_single_player){
            return true;
        }
        return false;
    }

    // --------------------------------------------
    void preBeginMatch() {
        m_taskSequencer.clear();
        m_taskManager.clear();
        _log("metagame preBeginMatch init");

        for(uint i=0; i<m_hd_taskSequencerArray.size(); ++i){
            m_hd_taskSequencerArray[i].clear();
        }

        _log("clearTrackers()");
        clearTrackers();
        m_gamePaused = false;

        if (isInServerMode()) {
            _log("isInServerMode()");
            addTracker(BanManager(this));
            getAdminManager().loadFromFile();
            getModeratorManager().loadFromFile();
        }
    }

    // --------------------------------------------
    bool isInServerMode() {
        return m_startServerCommand != "";
    }

    // --------------------------------------------
    protected void startServer() {
        if (m_startServerCommand != "") {
            getComms().send(m_startServerCommand);
        } else {
            _log("not starting server", 1);
        }
    }

    // --------------------------------------------
    void addTracker(Tracker@ tracker) {
        m_trackers.insertLast(tracker);
        tracker.onAdd();
        _log("tracker added, count " + m_trackers.size(), 1);
    }

    // --------------------------------------------
    protected void clearTrackers() {
        for (uint i = 0; i < m_trackers.size(); ++i) {
            Tracker@ tracker = m_trackers[i];
            tracker.onRemove();
        }
        m_trackers.resize(0);
        _log("tracker cleared, count " + m_trackers.size(), 1);
    }

    // --------------------------------------------
    protected void update(float time) {
        m_taskSequencer.update(time);
        m_taskManager.update(time);

        for(uint i=0; i<m_hd_taskSequencerArray.size(); ++i){
            m_hd_taskSequencerArray[i].update(time);
        }

        // maintain trackers
        for (uint i = 0; i < m_trackers.size(); ++i) {
            Tracker@ tracker = m_trackers[i];

            if (!tracker.hasStarted()) {
                tracker.start();
            }

            if (tracker.hasStarted()) {
                tracker.update(time);
            }

            if (tracker.hasEnded()) {
                trackerCompleted(tracker);
                _log("tracker " + i + " ended, removing from list", 1);

                // cleanup
                removeTrackerByKey(i);
                i--;
            }
        }
    }

    // --------------------------------------------
    protected void removeTrackerByKey(int i) {
        Tracker@ tracker = m_trackers[i];
        m_trackers.removeAt(i);
        tracker.onRemove();
    }

    // --------------------------------------------
    void removeTracker(Tracker@ tracker) {
        int i = m_trackers.findByRef(tracker);
        if (i != -1) {
            removeTrackerByKey(i);
        }
    }

    // --------------------------------------------
    void requestRestart() {
        _log("request_restart", 1);
        m_restartRequested = true;
    }

    // --------------------------------------------
    ModeratorManager@ getModeratorManager() const {
        return m_moderatorManager;
    }

    // --------------------------------------------
    AdminManager@ getAdminManager() const {
        return m_adminManager;
    }
}
