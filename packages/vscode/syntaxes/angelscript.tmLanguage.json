{"$schema": "https://raw.githubusercontent.com/martinring/tmlanguage/master/tmlanguage.json", "name": "AngelScript", "patterns": [{"include": "#comments"}, {"include": "#strings"}, {"include": "#numbers"}, {"include": "#keywords"}, {"include": "#types"}, {"include": "#operators"}, {"include": "#functions"}, {"include": "#preprocessor"}], "repository": {"comments": {"patterns": [{"name": "comment.line.double-slash.angelscript", "begin": "//", "end": "$"}, {"name": "comment.block.angelscript", "begin": "/\\*", "end": "\\*/"}]}, "strings": {"patterns": [{"name": "string.quoted.double.angelscript", "begin": "\"", "end": "\"", "patterns": [{"name": "constant.character.escape.angelscript", "match": "\\\\."}]}, {"name": "string.quoted.single.angelscript", "begin": "'", "end": "'", "patterns": [{"name": "constant.character.escape.angelscript", "match": "\\\\."}]}]}, "numbers": {"patterns": [{"name": "constant.numeric.float.angelscript", "match": "\\b\\d+\\.\\d+([eE][+-]?\\d+)?[fF]?\\b"}, {"name": "constant.numeric.integer.hexadecimal.angelscript", "match": "\\b0[xX][0-9a-fA-F]+\\b"}, {"name": "constant.numeric.integer.angelscript", "match": "\\b\\d+\\b"}]}, "keywords": {"patterns": [{"name": "keyword.control.angelscript", "match": "\\b(if|else|for|while|do|switch|case|default|break|continue|return|try|catch|finally)\\b"}, {"name": "keyword.other.angelscript", "match": "\\b(class|interface|namespace|enum|typedef|import|from|cast|is|in|out|inout|ref|auto|const|private|protected|public|final|override|abstract|property|get|set|shared|external|mixin|funcdef)\\b"}, {"name": "constant.language.angelscript", "match": "\\b(true|false|null)\\b"}, {"name": "storage.type.angelscript", "match": "\\b(void|bool|int|int8|int16|int32|int64|uint|uint8|uint16|uint32|uint64|float|double|string)\\b"}]}, "types": {"patterns": [{"name": "storage.type.primitive.angelscript", "match": "\\b(void|bool|int|int8|int16|int32|int64|uint|uint8|uint16|uint32|uint64|float|double|string)\\b"}, {"name": "entity.name.type.angelscript", "match": "\\b[A-Z][a-zA-Z0-9_]*\\b"}]}, "operators": {"patterns": [{"name": "keyword.operator.assignment.angelscript", "match": "(=|\\+=|-=|\\*=|/=|%=|&=|\\|=|\\^=|<<=|>>=|>>>=)"}, {"name": "keyword.operator.comparison.angelscript", "match": "(==|!=|<|>|<=|>=|is|!is)"}, {"name": "keyword.operator.arithmetic.angelscript", "match": "(\\+|-|\\*|/|%|\\+\\+|--)"}, {"name": "keyword.operator.logical.angelscript", "match": "(&&|\\|\\||!|\\^)"}, {"name": "keyword.operator.bitwise.angelscript", "match": "(&|\\||\\^|~|<<|>>|>>>)"}]}, "functions": {"patterns": [{"name": "entity.name.function.angelscript", "match": "\\b([a-zA-Z_][a-zA-Z0-9_]*)\\s*(?=\\()", "captures": {"1": {"name": "entity.name.function.angelscript"}}}]}, "preprocessor": {"patterns": [{"name": "meta.preprocessor.angelscript", "begin": "^\\s*#", "end": "$", "patterns": [{"name": "keyword.control.directive.angelscript", "match": "\\b(include|if|ifdef|ifndef|else|elif|endif|define|undef)\\b"}, {"name": "string.quoted.double.include.angelscript", "match": "\"[^\"]*\""}]}]}}, "scopeName": "source.angelscript"}