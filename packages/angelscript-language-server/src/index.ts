import {
    createServer,
    createConnection,
    loadTsdkByPath,
    createTypeScriptProject,
} from '@volar/language-server/node';
import { service } from './service';
import {
    angelscriptLanguagePlugin,
    AngelScriptVirtualCode,
} from './languagePlugin';

const connection = createConnection();
const server = createServer(connection);

connection.listen();

connection.onInitialize((params) => {
    console.log('AngelScript Language Server initializing...');
    console.log('Initialization params:', JSON.stringify(params, null, 2));

    const tsdk = loadTsdkByPath(
        params.initializationOptions.typescript.tsdk,
        params.locale,
    );

    const result = server.initialize(
        params,
        createTypeScriptProject(
            tsdk.typescript,
            tsdk.diagnosticMessages,
            () => ({
                languagePlugins: [angelscriptLanguagePlugin],
            }),
        ),
        [service],
    );

    console.log('AngelScript Language Server initialized');
    return result;
});

connection.onInitialized(server.initialized);
connection.onShutdown(server.shutdown);
