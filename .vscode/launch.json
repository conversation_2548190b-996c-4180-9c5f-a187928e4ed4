// A launch configuration that compiles the extension and then opens it inside a new window
{
	"version": "0.2.0",
	"configurations": [
		{
			"name": "Launch Client",
			"type": "extensionHost",
			"request": "launch",
			"runtimeExecutable": "${execPath}",
			"args": [
				"--extensionDevelopmentPath=${workspaceRoot}/packages/vscode"
			],
			"outFiles": [
				"${workspaceRoot}/packages/vscode/dist/*.js"
			],
			"preLaunchTask": {
				"type": "npm",
				"script": "watch"
			}
		},
		{
			"name": "Attach to Server",
			"type": "node",
			"request": "attach",
			"port": 6009,
			"restart": true,
			"outFiles": [
				"${workspaceRoot}/packages/vscode/dist/*.js"
			]
		},
	],
}
