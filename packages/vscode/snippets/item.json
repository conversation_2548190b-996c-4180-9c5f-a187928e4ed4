{"item.carry_item.base.define": {"prefix": "rwr-item-base-define", "description": "rwr carry_item base define", "body": ["<carry_item", "\ttime_to_live_out_in_the_open=\"$1\"", "\tdrop_count_factor_on_death=\"$2\"", "\tdrop_count_factor_on_player_death=\"$3\"", "\tplayer_death_drop_owner_lock_time=\"$4\"", "\ton_ground_up=\"${5|0 1 0|}\"", ">", "$6", "</carry_item>", "$7"]}, "item.carry_item.define": {"prefix": "rwr-item-define", "description": "rwr carry_item define", "body": ["<carry_item", "\tfile=\"$1\"", "\tname=\"$2\"", "\tname=\"$3\"", ">", "$4", "</carry_item>", "$5"]}, "item.carry_item.commonness": {"prefix": "rwr-item-commonness", "description": "rwr carry_item commonness", "body": ["<commonness value=\"$1\" in_stock=\"${2|1|}\" can_respawn_with=\"${3|0|}\" />"]}}